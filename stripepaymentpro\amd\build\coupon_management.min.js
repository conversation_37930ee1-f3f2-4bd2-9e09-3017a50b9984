define(["exports","core/ajax","core/notification","core/str"],function(e,t,n,o){"use strict";function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r=s(t),a=s(n);const i=async(e,t,n)=>{try{const s=await o.get_string("confirm_delete_coupon","enrol_stripepaymentpro");if(!confirm(s))return;const i=n.innerHTML;n.innerHTML='<i class="fa fa-spinner fa-spin"></i>',n.disabled=!0;const c={methodname:"moodle_stripepaymentpro_deactivate_coupon",args:{courseid:e,couponid:t}},l=await r.default.call([c])[0];if(l.success){const e=n.closest("tr"),t=e.querySelector("td[rowspan]");if(t){const n=parseInt(t.getAttribute("rowspan"));if(n>1){const o=e.nextElementSibling;o&&(t.setAttribute("rowspan",n-1),o.insertBefore(t,o.firstChild))}}else{const t=(e=>{let t=e;for(;t&&!t.querySelector("td[rowspan]");)t=t.previousElementSibling;return t})(e);if(t){const e=t.querySelector("td[rowspan]");if(e){const t=parseInt(e.getAttribute("rowspan"));e.setAttribute("rowspan",t-1)}}}e.remove();const s=await o.get_string("coupon_deleted_success","enrol_stripepaymentpro");a.default.addNotification({message:s,type:"success"})}else{n.innerHTML=i,n.disabled=!1;const e=l.message||await o.get_string("error_deleting_coupon","enrol_stripepaymentpro");a.default.addNotification({message:e,type:"error"})}}catch(e){n.innerHTML='<i class="fa fa-trash"></i>',n.disabled=!1;const t=await o.get_string("error_deleting_coupon","enrol_stripepaymentpro");a.default.addNotification({message:t+": "+e.message,type:"error"})}},c=async(e,t)=>{try{const n=await o.get_string("confirm_delete_all_coupons","enrol_stripepaymentpro");if(!confirm(n))return;const s=t.innerHTML;t.innerHTML='<i class="fa fa-spinner fa-spin"></i>',t.disabled=!0;const i={methodname:"moodle_stripepaymentpro_deactivate_all_coupons",args:{courseid:e}},c=await r.default.call([i])[0];if(c.success){const e=await o.get_string("all_coupons_deleted_success","enrol_stripepaymentpro");a.default.addNotification({message:e,type:"success"}),window.location.reload()}else{t.innerHTML=s,t.disabled=!1;const e=c.message||await o.get_string("error_deleting_coupons","enrol_stripepaymentpro");a.default.addNotification({message:e,type:"error"})}}catch(e){t.innerHTML='<i class="fa fa-trash-alt"></i>',t.disabled=!1;const n=await o.get_string("error_deleting_coupons","enrol_stripepaymentpro");a.default.addNotification({message:n+": "+e.message,type:"error"})}},l=()=>{const e=document.querySelector(".table-responsive");if(e&&!document.querySelector("#coupon-search")){const t=document.createElement("div");t.style.marginBottom="15px",t.innerHTML='\n            <input type="text" id="coupon-search" placeholder="Search coupons..."\n                   class="form-control" style="width: 300px; display: inline-block;">\n        ',e.parentNode.insertBefore(t,e),document.getElementById("coupon-search").addEventListener("input",e=>{const t=e.target.value.toLowerCase();document.querySelectorAll(".table tbody tr").forEach(e=>{const n=e.textContent.toLowerCase().includes(t);e.style.display=n?"":"none"})})}};window.handleCouponDelete=i,window.handleDeleteAllCoupons=c,e.deleteAllCoupons=c,e.deleteCoupon=i,e.init=()=>{const e=document.getElementById("all_coupon_button"),t=document.getElementById("generate_coupons_button"),n=document.getElementById("all_coupons_section"),o=document.getElementById("generate_coupon_section");if(!(e&&t&&n&&o))return;const s=()=>{n.style.display="block",o.style.display="none",e.classList.remove("btn-secondary"),e.classList.add("btn-primary","active"),t.classList.remove("btn-primary","active"),t.classList.add("btn-secondary")};e.addEventListener("click",s),t.addEventListener("click",()=>{n.style.display="none",o.style.display="block",t.classList.remove("btn-secondary"),t.classList.add("btn-primary","active"),e.classList.remove("btn-primary","active"),e.classList.add("btn-secondary")}),s(),l()},Object.defineProperty(e,"__esModule",{value:!0})});
