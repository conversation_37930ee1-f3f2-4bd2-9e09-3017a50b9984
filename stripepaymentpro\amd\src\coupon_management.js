// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Coupon management functionality for stripepaymentpro plugin
 *
 * @module     enrol_stripepaymentpro/coupon_management
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

import ajax from 'core/ajax';
import notification from 'core/notification';
import {get_string} from 'core/str';

/**
 * Initialize coupon management functionality
 */
export const init = () => {
    // Tab switching functionality
    const allCouponsButton = document.getElementById('all_coupon_button');
    const generateCouponsButton = document.getElementById('generate_coupons_button');
    const allCouponsSection = document.getElementById('all_coupons_section');
    const generateCouponsSection = document.getElementById('generate_coupon_section');

    if (!allCouponsButton || !generateCouponsButton || !allCouponsSection || !generateCouponsSection) {
        return; // Elements not found, exit gracefully
    }

    /**
     * Show all coupons section
     */
    const showAllCoupons = () => {
        allCouponsSection.style.display = 'block';
        generateCouponsSection.style.display = 'none';
        allCouponsButton.classList.remove('btn-secondary');
        allCouponsButton.classList.add('btn-primary', 'active');
        generateCouponsButton.classList.remove('btn-primary', 'active');
        generateCouponsButton.classList.add('btn-secondary');
    };

    /**
     * Show generate coupons section
     */
    const showGenerateCoupons = () => {
        allCouponsSection.style.display = 'none';
        generateCouponsSection.style.display = 'block';
        generateCouponsButton.classList.remove('btn-secondary');
        generateCouponsButton.classList.add('btn-primary', 'active');
        allCouponsButton.classList.remove('btn-primary', 'active');
        allCouponsButton.classList.add('btn-secondary');
    };

    // Event listeners
    allCouponsButton.addEventListener('click', showAllCoupons);
    generateCouponsButton.addEventListener('click', showGenerateCoupons);

    // Initialize with all coupons view
    showAllCoupons();

    // Initialize search functionality
    initializeSearch();
};

/**
 * Helper function to find the first row in a course group
 * @param {HTMLElement} currentRow - The current table row
 * @returns {HTMLElement|null} The first row in the course group
 */
const findFirstRowInCourseGroup = (currentRow) => {
    let row = currentRow;
    // Go backwards to find the row with rowspan (course name cell)
    while (row && !row.querySelector('td[rowspan]')) {
        row = row.previousElementSibling;
    }
    return row;
};

/**
 * Delete a single coupon
 * @param {string} courseId - Course ID (Stripe Product ID)
 * @param {string} couponId - Coupon ID
 * @param {HTMLElement} button - Delete button element
 */
export const deleteCoupon = async (courseId, couponId, button) => {
    try {
        const confirmMessage = await get_string('confirm_delete_coupon', 'enrol_stripepaymentpro');
        if (!confirm(confirmMessage)) {
            return;
        }

        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
        button.disabled = true;

        const request = {
            methodname: 'moodle_stripepaymentpro_deactivate_coupon',
            args: {
                courseid: courseId,
                couponid: couponId
            }
        };

        const result = await ajax.call([request])[0];

        if (result.success) {
            const row = button.closest('tr');
            const courseCell = row.querySelector('td[rowspan]');

            // If this row has the course name cell (first coupon), handle rowspan
            if (courseCell) {
                const currentRowspan = parseInt(courseCell.getAttribute('rowspan'));
                if (currentRowspan > 1) {
                    // Move course cell to next row and decrease rowspan
                    const nextRow = row.nextElementSibling;
                    if (nextRow) {
                        courseCell.setAttribute('rowspan', currentRowspan - 1);
                        nextRow.insertBefore(courseCell, nextRow.firstChild);
                    }
                }
            } else {
                // This is not the first row, just decrease rowspan of course cell
                const firstRowInGroup = findFirstRowInCourseGroup(row);
                if (firstRowInGroup) {
                    const courseCellInGroup = firstRowInGroup.querySelector('td[rowspan]');
                    if (courseCellInGroup) {
                        const currentRowspan = parseInt(courseCellInGroup.getAttribute('rowspan'));
                        courseCellInGroup.setAttribute('rowspan', currentRowspan - 1);
                    }
                }
            }

            // Remove the row
            row.remove();
            
            const successMessage = await get_string('coupon_deleted_success', 'enrol_stripepaymentpro');
            notification.addNotification({
                message: successMessage,
                type: 'success'
            });
        } else {
            button.innerHTML = originalHTML;
            button.disabled = false;
            const errorMessage = result.message || await get_string('error_deleting_coupon', 'enrol_stripepaymentpro');
            notification.addNotification({
                message: errorMessage,
                type: 'error'
            });
        }
    } catch (error) {
        button.innerHTML = '<i class="fa fa-trash"></i>';
        button.disabled = false;
        const errorMessage = await get_string('error_deleting_coupon', 'enrol_stripepaymentpro');
        notification.addNotification({
            message: errorMessage + ': ' + error.message,
            type: 'error'
        });
    }
};

/**
 * Delete all coupons for a course
 * @param {string} courseId - Course ID (Stripe Product ID)
 * @param {HTMLElement} button - Delete all button element
 */
export const deleteAllCoupons = async (courseId, button) => {
    try {
        const confirmMessage = await get_string('confirm_delete_all_coupons', 'enrol_stripepaymentpro');
        if (!confirm(confirmMessage)) {
            return;
        }

        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
        button.disabled = true;

        const request = {
            methodname: 'moodle_stripepaymentpro_deactivate_all_coupons',
            args: {
                courseid: courseId
            }
        };

        const result = await ajax.call([request])[0];

        if (result.success) {
            const successMessage = await get_string('all_coupons_deleted_success', 'enrol_stripepaymentpro');
            notification.addNotification({
                message: successMessage,
                type: 'success'
            });
            window.location.reload();
        } else {
            button.innerHTML = originalHTML;
            button.disabled = false;
            const errorMessage = result.message || await get_string('error_deleting_coupons', 'enrol_stripepaymentpro');
            notification.addNotification({
                message: errorMessage,
                type: 'error'
            });
        }
    } catch (error) {
        button.innerHTML = '<i class="fa fa-trash-alt"></i>';
        button.disabled = false;
        const errorMessage = await get_string('error_deleting_coupons', 'enrol_stripepaymentpro');
        notification.addNotification({
            message: errorMessage + ': ' + error.message,
            type: 'error'
        });
    }
};



/**
 * Initialize search functionality for coupon table
 */
const initializeSearch = () => {
    const tableContainer = document.querySelector('.table-responsive');
    if (tableContainer && !document.querySelector('#coupon-search')) {
        const searchContainer = document.createElement('div');
        searchContainer.style.marginBottom = '15px';
        searchContainer.innerHTML = `
            <input type="text" id="coupon-search" placeholder="Search coupons..."
                   class="form-control" style="width: 300px; display: inline-block;">
        `;
        tableContainer.parentNode.insertBefore(searchContainer, tableContainer);

        document.getElementById('coupon-search').addEventListener('input', (event) => {
            const searchTerm = event.target.value.toLowerCase();
            document.querySelectorAll('.table tbody tr').forEach(row => {
                const isVisible = row.textContent.toLowerCase().includes(searchTerm);
                row.style.display = isVisible ? '' : 'none';
            });
        });
    }
};

// Make functions available globally for onclick handlers (backwards compatibility)
window.handleCouponDelete = deleteCoupon;
window.handleDeleteAllCoupons = deleteAllCoupons;
