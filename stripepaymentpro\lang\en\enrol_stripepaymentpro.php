<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
/**
 * Strings for component 'enrol_stripepaymentpro', language 'en'.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
$string['action'] = 'Action';
$string['activelicense'] = 'Activate License';
$string['apikey'] = 'API Key';
$string['stripepaymentpro_couponmanagement_menu_name'] = 'Stripe Payment Pro - Coupon Management';
$string['apikey_desc'] = ' from shop';
$string['applycode'] = 'Apply Code';
$string['areyousure'] = ' Are you sure?';
$string['areyousure_des'] = ' If you cancel the subscription, you will lose access course updates and premium support.';
$string['assignrole'] = 'Assign role';
$string['buy_now'] = 'Buy Now';
$string['cancelfailed'] = 'Subscription cancelation failed.';
$string['cancelsubcription'] = 'Cancel Subcription';
$string['confermcancelsubcription'] = 'Conferm Cancel Subcription';
$string['cancelsucess'] = 'Subscription successfully cancelled.';
$string['canntenrol'] = 'Enrolment is disabled or inactive';
$string['charge_description1'] = "create customer for email receipt";
$string['charge_description2'] = 'Charge for Course Enrolment Cost.';
$string['checking'] = ' checking...';
$string['connectionfailed'] = 'Network communication with Stripe failed';
$string['cost'] = 'Sign up fee';
$string['costerror'] = 'The enrolment cost is not numeric';
$string['costorkey'] = 'Please choose one of the following methods of enrolment.';
$string['couponapplied'] = 'Coupon Applied';
$string['couponcode'] = 'Coupon Code';
$string['couponcodedescription'] = 'If you have any coupon please apply here';
$string['couponnotvalidforthisproduct'] = 'This coupon is not valid for the selected course or product.';
$string['coursename'] = 'Course Name';
$string['create_user_token'] = 'REQUIRED: To make Stripe callback work, you must enable Moodle REST protocol on your site';
$string['currency'] = 'Currency';
$string['deactivelicense'] = 'Deactivate License';
$string['defaultrole'] = 'Default role assignment';
$string['defaultrole_desc'] = 'Select role which should be assigned to users during Stripe enrolments';
$string['enable_coupon_section'] = 'Enable coupon section';
$string['enable_automatic_tax'] = 'Enable automatic tax';
$string['enabled_rest_protocol'] = ' You must also create a token of moodle_enrol_stripepayment service with Administrator privilege ';
$string['enrol'] = 'Enrol';
$string['enrol_btn_color'] = 'Choose Enroll button Color';
$string['enrol_btn_color_des'] = 'Choose your own custom Color scheme for the Enroll Button.';
$string['enrol_now'] = 'Enrol Now';
$string['enrolenddate'] = 'End date';
$string['enrolenddate_help'] = 'If enabled, users can be enrolled until this date only.';
$string['enrolenddaterror'] = 'Enrolment end date cannot be earlier than start date';
$string['enrollsuccess'] = 'Thankyou! Now you are enrolled into the course ';
$string['enrolperiod'] = 'Enrolment duration';
$string['enrolperiod_desc'] = 'Default length of time that the enrolment is valid. If set to zero, the enrolment duration will be unlimited by default.';
$string['enrolperiod_help'] = 'Length of time that the enrolment is valid, starting with the moment the user is enrolled. If disabled, the enrolment duration will be unlimited.';
$string['enrolstartdate'] = 'Start date';
$string['enrolstartdate_help'] = 'If enabled, users can be enrolled from this date onward only.';
$string['error'] = 'Error! ';
$string['expired'] = 'Expired ';
$string['expiredaction'] = 'Enrolment expiration action';
$string['expiredaction_help'] = 'Select action to carry out when user enrolment expires. Please note that some user data and settings are purged from course during course unenrolment.';
$string['final_cost'] = 'Final Cost';
$string['from_here'] = 'from here';
$string['generalsettings'] = 'General Settings';
$string['invalidcontextid'] = 'Not a valid context id! ';
$string['invalidcoupon'] = 'Invalid coupon!';
$string['invalidcouponcode'] = 'Invalid Coupon Code';
$string['invalidcouponcodevalue'] = 'Coupon Code {$a} is not valid!';
$string['invalidcourseid'] = 'Not a valid course id!';
$string['invalidinstance'] = 'Not a valid instance id!';
$string['invalidrequest'] = 'Invalid Request!';
$string['invalidstripeparam'] = 'Invalid parameters were supplied to Stripe API';
$string['invaliduserid'] = 'Not a valid user id! ';
$string['licenseexdate'] = 'License Expiry Date';
$string['licensesetting'] = 'License Settings';
$string['licensestatus'] = 'License Status:';
$string['mailadmins'] = 'Notify admin';
$string['mailstudents'] = 'Notify students';
$string['mailteachers'] = 'Notify teachers';
$string['maxenrolled'] = 'Max enrolled users';
$string['maxenrolled_help'] = 'Specifies the maximum number of users that can stripepaymentpro enrol. 0 means no limit.';
$string['maxenrolledreached'] = 'Maximum number of users allowed to stripepaymentpro-enrol was already reached.';
$string['maxenrolledhelp'] = 'Stripe enrolment messages';
$string['messageprovider:stripepaymentpro_enrolment'] = 'Message Provider';
$string['newcost'] = 'New Cost';
$string['noactivationremain'] = 'No Activations Remaining ';
$string['noapi'] = 'Enter API Key';
$string['nocost'] = 'There is no recurring cost associated with enrolling in this course!';
$string['notstripeerror'] = 'Something else happened, completely unrelated to Stripe';
$string['pleasewait'] = 'Please wait...';
$string['pluginname'] = 'Stripe Payment Pro';
$string['pluginname_desc'] = 'The Stripe module allows you to set up paid courses.  If the cost for any course is zero, then students are not asked to pay for entry.  There is a site-wide cost that you set here as a default for the whole site and then a course setting that you can set for each course individually. The course cost overrides the site cost.';
$string['productid'] = 'Product ID';
$string['productid_desc'] = 'The parchesed ';
$string['publishablekey'] = 'Stripe Publishable Key';
$string['publishablekey_desc'] = 'The API Publishable Key of Stripe account';
$string['recurringtotal'] = 'Recurring Total';
$string['renewalcost'] = 'Renewal Cost';
$string['renewalintarval'] = 'Renewal Interval';
$string['renewalintarvalnum'] = 'Renewal Interval Period';
$string['renewalintarvalnum_help'] = 'The number of intervals between subscription billings. For example, Intarval=month and Intarval Period=3 bills every 3 months. Maximum of one year interval allowed (1 year, 12 months, or 52 weeks).';
$string['renewalintarvalnumerror'] = 'The renewal interval period must be a positive number';
$string['renewnow'] = 'Renew Now ';
$string['secretkey'] = 'Stripe Secret Key';
$string['secretkey_desc'] = 'The API Secret Key of Stripe account';
$string['sendpaymentbutton'] = 'Send payment via Stripe';
$string['sessioncreated'] = 'Checkout Session created successfully!';
$string['sessioncreatefail'] = 'Checkout Session creation failed! ';
$string['status_desc'] = 'Allow users to use Stripe to enrol into a course by default.';
$string['stripe:config'] = 'Configure Stripe enrol instances';
$string['stripe:manage'] = 'Manage enrolled users';
$string['stripe:unenrol'] = 'Unenrol users from course';
$string['stripe:unenrolself'] = 'Unenrol self from the course';
$string['stripe_sorry'] = "Sorry, you can not use the script that way.";
$string['stripeaccepted'] = 'Stripe payments accepted';
$string['stripeauthfail'] = 'Authentication with Stripe API failed';
$string['stripeerror'] = 'Stripe Error ';
$string['stripepaymentpro:config'] = 'Configure stripepaymentpro';
$string['stripepaymentpro:manage'] = 'Manage stripepaymentpro';
$string['stripepaymentpro:unenrol'] = 'Unenrol stripepaymentpro';
$string['stripepaymentpro:unenrolself'] = 'Unenrolself stripepaymentpro';
$string['stripesettings'] = 'Stripe Payment Settings';
$string['stripesettings_des'] = 'Kindly set the following keys on our main plugin <a href="' . $CFG->wwwroot . '/admin/settings.php?section=enrolsettingsstripepayment" target="_blank"> Stripe Payment </a>to this Pro to work';
$string['subid'] = ' Subscription ID';
$string['subscription_url'] = 'https://dualcube.com/my-account/api-keys/';
$string['subscriptionrenew_url'] = 'https://dualcube.com/my-account/view-subscription/';
$string['subscriptiontable'] = 'Subscription Table';
$string['subtotal'] = 'Subtotal';
$string['sumthingwrong'] = 'Something went wrong';
$string['taskend'] = 'enrol stripepaymentpro unenrol task task finished';
$string['taskstart'] = 'Hey, admin enrol stripepaymentpro unenrol task is running';
$string['thankscontinue'] = ' Thanks for continuation.';
$string['token_empty_error'] = 'Web service token could not be empty';
$string['trialperiod'] = 'Trial Period (min 2 days)';
$string['unenrol'] = ' got unenroled from ';
$string['unenrolselfconfirm'] = 'Do you really want to unenrol yourself from course "{$a}"?';
$string['unmatchedcourse'] = 'Course Id does not match to the course settings, received: ';
$string['username'] = ' User Name';
$string['webservice_token_string'] = 'User Token';
$string['invalidsubid'] = ' Given sabcription ID is invalid';
$string['paymentcreatefail'] = 'payment intent create faild';
$string['stripe_checkout'] = 'Stripe Checkout';
$string['stripe_elements'] = 'Stripe Elements';
$string['payment_gateway_type_desc'] = 'Choose the type of Stripe payment gateway to use:';
$string['payment_gateway_type'] = 'Payment Gateway Type';
$string['status'] = 'Status';
$string['startdate'] = 'Start date';
$string['notactive'] = 'Inactive';
$string['nextpayment'] = 'Next payment';
$string['coupon_settings_desc'] = 'Generate and manage Stripe coupon codes directly from your platform. Easily create assign coupons from ';
$string['coupon_settings'] = 'Coupon Creation & Assignment';
$string['duplicatedata'] = 'Duplicate data submited';
$string['course'] = 'course';
$string['coupon'] = 'coupon';
$string['addanother'] = 'addanother';
$string['generatecoupon'] = 'Generate coupon';
$string['coupon_duration_in_month'] = 'Coupon duration (months)';
$string['coupon_percent_off'] = 'Coupon percentage off';
$string['add_coupon_setting'] = 'Add coupon';
$string['invalid_duration'] = 'Invalid coupon duration.';
$string['invalid_percent_off'] = 'Invalid coupon percentage off.';
$string['coupon_created_success'] = 'Coupon created successfully.';
$string['coupon_setting_link_text'] = 'here';
$string['generate_coupon_desc'] = 'Go to the generate coupon page:';
$string['add_coupon_to_course'] = 'Add coupon to course';
$string['not_valid_coupon_for_course'] = 'This coupon is not valid for this course';
$string['deactivate_coupon'] = 'deactive';
$string['deactivate_all_coupons'] = 'Deactivate all coupons';
$string['recurringcost'] = 'Recurring cost';
$string['subscription_plan'] = 'Subscription plan';
$string['next_renewal']= 'Next renewal';
$string['cancelsuccess'] = 'subscription cancellation successful';
$string['all_coupons'] = 'All Coupons';
$string['generate_coupons'] = 'Generate Coupon';
$string['assign_coupon'] = 'Assign Coupon';



$string['no_coupon_found'] = 'You have not added any coupon yet!!';
$string['coupon_name'] = 'Coupon name';
$string['coupon_name_required'] = 'Coupon name cant be empty';
$string['coupon_types'] = 'Discount types';
$string['discount_type_fixed'] = 'Fixed discount';
$string['discount_type_percentage'] = 'Percentage discount';
$string['discount_amount'] = 'Discount amount';
$string['discount_amount_non_zero'] = 'Discount value must be grater than 0';
$string['coupon_expiry'] = 'Coupon expiry';
$string['coupon_duration'] = 'Duration';
$string['coupon_duration_forever'] = 'Forever';
$string['coupon_duration_once'] = 'Once';
$string['coupon_duration_multiple_months'] = 'Multiple months';
$string['coupon_duration_multiple_months_val'] = 'Number of months';
$string['coupon_duration_multiple_months_val_non_zero'] = 'Number of months should be more than 0';
$string['coupon_currency'] = 'Coupon currency';
$string['coupon_for_all_courses'] = 'All Courses';
$string['coupon_course_assignment'] = 'Coupon for course';

// New strings for refactored templates and controllers
$string['subscription_status'] = 'Stripe Subscription Status';
$string['coupon_management'] = 'Stripe Coupon Management';
$string['no_subscriptions_found'] = 'No subscriptions found.';
$string['course_name'] = 'Course Name';
$string['coupons'] = 'Coupons';
$string['coupon_id'] = 'Coupon ID';
$string['actions'] = 'Actions';
$string['coupon_created_success'] = 'Coupon created successfully';
$string['invalidsessionid'] = 'Invalid session ID';
$string['stripeerror'] = 'Stripe error: {$a}';
$string['paymentnotsuccessful'] = 'Payment was not successful. Please try again.';
$string['coupon_name_required'] = 'Coupon name is required';
$string['invalid_coupon_expiry'] = 'Invalid coupon expiry date';
$string['invalid_coupon_amount'] = 'Invalid coupon amount';
$string['invalid_discount_percentage'] = 'Discount percentage must be less than 100';

// Enhanced coupon validation strings
$string['enter_coupon_code'] = 'Enter coupon code';
$string['coupon_code_required'] = 'Please enter a coupon code';
$string['coupon_code_too_short'] = 'Coupon code must be at least 3 characters long';
$string['coupon_code_too_long'] = 'Coupon code is too long (maximum 50 characters)';
$string['coupon_code_invalid_chars'] = 'Coupon code contains invalid characters. Only letters, numbers, hyphens and underscores are allowed';
$string['coupon_not_found'] = 'Coupon not found';
$string['coupon_expired'] = 'This coupon has expired';
$string['coupon_not_applicable'] = 'This coupon is not applicable to this course';
$string['coupon_usage_limit_reached'] = 'This coupon has reached its usage limit';
$string['coupon_not_valid'] = 'This coupon is no longer valid';
$string['coupon_validation_error'] = 'Error validating coupon. Please try again';
$string['coupon_applied_successfully'] = 'Coupon applied successfully!';
$string['coupon_verifying'] = 'Verifying coupon...';
$string['coupon_course_specific_warning'] = 'This coupon is for a specific course. Verifying applicability...';
$string['invalidcoupon'] = 'Invalid coupon code';
$string['coupon_help_text'] = 'Enter a valid coupon code to receive a discount on your enrollment';
$string['checking'] = 'Checking...';

// Coupon management interface strings
$string['all_coupons'] = 'All Coupons';
$string['generate_coupons'] = 'Generate Coupons';
$string['existing_coupons'] = 'Existing Coupons';
$string['create_new_coupon'] = 'Create New Coupon';
$string['no_coupon_found'] = 'No coupons found. Create your first coupon using the "Generate Coupons" tab.';
$string['deactivate_all_coupons'] = 'Delete All';

// Coupon management JavaScript strings
$string['confirm_delete_coupon'] = 'Are you sure you want to delete this coupon? This will remove it from both the database and Stripe.';
$string['confirm_delete_all_coupons'] = 'Are you sure you want to delete ALL coupons for this course? This will remove them from both the database and Stripe.';
$string['coupon_deleted_success'] = 'Coupon deleted successfully.';
$string['all_coupons_deleted_success'] = 'All coupons deleted successfully.';
$string['error_deleting_coupon'] = 'Error deleting coupon';
$string['error_deleting_coupons'] = 'Error deleting coupons';
$string['couponexpired'] = 'This coupon has expired.';
$string['invalidinstanceid'] = 'Invalid instance ID provided.';