<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Coupon management controller for stripepaymentpro plugin
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\controller;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');

use \Stripe\StripeClient;

/**
 * Controller for coupon management functionality
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class coupon_controller {

    /**
     * Stripe client for communication with Stripe
     */
    private $stripe;

    /**
     * Plugin instance
     */
    private $plugin;

    /**
     * Core plugin instance
     */
    private $plugincore;

    /**
     * Constructor
     */
    public function __construct() {
        $this->plugin = enrol_get_plugin('stripepaymentpro');
        $this->plugincore = enrol_get_plugin('stripepayment');
        $this->stripe = new StripeClient(get_config('enrol_stripepayment', 'secretkey'));
    }

    /**
     * Get all coupons grouped by course
     *
     * @return array Coupon data for template
     */
    public function get_coupons_data() {
        global $DB;

        // Fetch coupons with course information in a single query for better performance
        $sql = "SELECT c.*, e.id as enrol_id, co.fullname as course_name
                FROM {enrol_stripepro_coupons} c
                LEFT JOIN {enrol} e ON c.stripe_product_id = e.customtext2 AND e.enrol = 'stripepaymentpro'
                LEFT JOIN {course} co ON e.courseid = co.id
                ORDER BY c.timecreated DESC";

        $coupon_records = $DB->get_records_sql($sql);

        // Group coupons by course
        $coupons_by_course = [];
        $coupons_list = [];
        $courses = [];

        foreach ($coupon_records as $record) {
            // Handle empty stripe_product_id values consistently
            $product_id = $record->stripe_product_id !== null ? $record->stripe_product_id : '';
            $coupons_by_course[$product_id][] = $record->couponid;
            $coupons_list[$record->couponid] = $record;

            // Store course information if available
            if ($record->course_name && !isset($courses[$product_id])) {
                $courses[$product_id] = (object) [
                    'customtext2' => $product_id,
                    'id' => $record->enrol_id,
                    'fullname' => $record->course_name
                ];
            }
        }

        // Add "All Courses" option for coupons without specific product assignment
        if (isset($coupons_by_course[''])) {
            $courses[''] = (object) ["customtext2" => '', "id" => 0, "fullname" => "All Courses"];
        }

        return [
            'coupons_by_course' => $coupons_by_course,
            'coupons_list' => $coupons_list,
            'courses' => $courses,
            'has_coupons' => !empty($coupons_by_course)
        ];
    }

    /**
     * Get courses with stripepaymentpro enrollment method
     *
     * @return array Course list for form
     */
    public function get_stripe_courses() {
        global $DB;

        $courses_with_stripe = $DB->get_records_sql(
            "SELECT DISTINCT e.customtext2, c.fullname
             FROM {enrol} e
             JOIN {course} c ON e.courseid = c.id
             WHERE e.enrol = 'stripepaymentpro'
               AND e.customtext2 IS NOT NULL
               AND e.customtext2 != ''
             ORDER BY c.fullname"
        );

        $stripe_course_list = ['0' => 'All Courses']; // Add default option
        foreach ($courses_with_stripe as $course) {
            if (!empty($course->customtext2)) {
                $stripe_course_list[$course->customtext2] = $course->fullname;
            }
        }

        return $stripe_course_list;
    }

    /**
     * Create a new coupon
     * 
     * @param object $formdata Form data from coupon creation form
     * @return bool Success status
     */
    public function create_coupon($formdata) {
        global $DB;

        $stripe_create_coupon_params = [];
        
        $stripe_create_coupon_params['name'] = $formdata->coupon_name;
    
        if ($formdata->coupon_expiry > time()) {
            $stripe_create_coupon_params['redeem_by'] = $formdata->coupon_expiry;
        }

        if ($formdata->coupon_types == "amount_off") {
            $stripe_create_coupon_params['amount_off'] = intval($formdata->discount_amount) * 
                $this->plugin->get_fractional_unit_amount($formdata->coupon_currency);
            $stripe_create_coupon_params['currency'] = $formdata->coupon_currency;
        } else if ($formdata->coupon_types == "percent_off") {
            $stripe_create_coupon_params['percent_off'] = $formdata->discount_amount;
        }

        $stripe_create_coupon_params['duration'] = $formdata->coupon_duration;

        if ($formdata->coupon_duration == "repeating") {
            $stripe_create_coupon_params['duration_in_months'] = $formdata->coupon_duration_multiple_months_val;
        }

        if ($formdata->coupon_course_assignment != 0) {
            $stripe_create_coupon_params['applies_to']['products'] = [$formdata->coupon_course_assignment];
        }

        try {
            $coupon = $this->stripe->coupons->create($stripe_create_coupon_params);
            
            if ($coupon != null && $coupon->id != "") {
                $record = new \stdClass();
                $record->couponid = $coupon->id;
                $record->coupon_name = $coupon->name;
                if (!is_null($coupon->amount_off) && !is_null($coupon->currency)) {
                    $record->amount_off = ($coupon->amount_off / $this->plugin->get_fractional_unit_amount(strtoupper($coupon->currency)));
                    $record->currency = $coupon->currency;
                } else {
                    $record->amount_off = null;
                    $record->currency = null;
                }
                $record->percent_off = $coupon->percent_off ?? null;
                $record->duration = $coupon->duration;
                $record->no_of_months = $coupon->duration_in_months ? $coupon->duration_in_months : 0;
                $record->stripe_product_id = $formdata->coupon_course_assignment;
                $record->timecreated = $coupon->created;
                $record->coupon_expiry = $coupon->redeem_by ? $coupon->redeem_by : 0;

                if (!$DB->record_exists('enrol_stripepro_coupons', ['couponid' => $record->couponid])) {
                    $DB->insert_record('enrol_stripepro_coupons', $record);
                    return true;
                } else {
                    \core\notification::error(get_string('duplicatedata', 'enrol_stripepaymentpro'));
                    return false;
                }
            }
        } catch (\Exception $e) {
            \core\notification::error($e->getMessage());
            return false;
        }

        return false;
    }

    /**
     * Prepare coupon data for template rendering
     *
     * @param array $coupons_by_course Coupons grouped by course
     * @param array $coupons_list All coupons data
     * @param array $courses Course data
     * @return array Template data
     */
    public function prepare_coupon_template_data($coupons_by_course, $coupons_list, $courses) {
        $template_data = [];

        foreach ($coupons_by_course as $course_id => $coupons) {
            // Determine course name and data
            $course_name = 'All Courses';
            if (!empty($course_id) && isset($courses[$course_id])) {
                $course_name = $courses[$course_id]->fullname;
            } else if (!empty($course_id)) {
                $course_name = 'Unknown Course (Product ID: ' . $course_id . ')';
            }

            $course_data = [
                'course_name' => htmlspecialchars($course_name),
                'course_id' => $course_id,
                'coupons' => [],
                'has_multiple_coupons' => count($coupons) > 1
            ];

            foreach ($coupons as $index => $coupon_id) {
                if (!isset($coupons_list[$coupon_id])) {
                    continue; // Skip if coupon not found
                }

                $coupon = $coupons_list[$coupon_id];

                // Format discount text
                $discount_text = $this->format_discount_text($coupon);

                // Format duration text
                $duration_text = ($coupon->duration == "repeating") ?
                    " for " . $coupon->no_of_months . " months" : " " . $coupon->duration;

                // Format expiry text
                $expiry_text = $coupon->coupon_expiry > 0 ?
                    " Expiry: " . userdate($coupon->coupon_expiry) : " Expiry: Never";

                $creation_date = userdate($coupon->timecreated);

                $course_data['coupons'][] = [
                    'coupon_id' => $coupon_id,
                    'coupon_name' => htmlspecialchars($coupon->coupon_name ?? 'Unknown Coupon'),
                    'discount_text' => $discount_text,
                    'duration_text' => $duration_text,
                    'expiry_text' => $expiry_text,
                    'full_description' => $discount_text . $duration_text . $expiry_text,
                    'is_first_coupon' => $index === 0,
                    'course_name' => $course_data['course_name'],
                    'course_id' => $course_data['course_id'],
                    'total_coupons' => count($coupons),
                    'creation_date' => $creation_date
                ];
            }

            $template_data[] = $course_data;
        }

        return $template_data;
    }

    /**
     * Format discount text for display
     *
     * @param object $coupon Coupon data
     * @return string Formatted discount text
     */
    private function format_discount_text($coupon) {
        if ($coupon->percent_off > 0) {
            return $coupon->percent_off . '% off';
        } else if (isset($coupon->amount_off) && $coupon->amount_off > 0) {
            $currency_symbol = html_entity_decode($this->plugincore->show_currency_symbol(strtolower($coupon->currency)));
            return $currency_symbol . $coupon->amount_off . ' off';
        }
        return 'Discount';
    }
}
