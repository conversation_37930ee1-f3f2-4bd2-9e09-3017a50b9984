<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Main page controller for stripepaymentpro plugin
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\controller;

defined('MOODLE_INTERNAL') || die();

/**
 * Main page controller for handling different page requests
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class page_controller {

    /**
     * Handle subscription management page
     * 
     * @param int $userid User ID (optional)
     * @return void
     */
    public static function handle_subscription_page($userid = 0) {
        global $CFG, $PAGE, $OUTPUT, $USER;

        require_login();
        
        $PAGE->set_context(\context_system::instance());
        $PAGE->set_title(get_string('subscription_status', 'enrol_stripepaymentpro'));
        $PAGE->set_heading(get_string('subscription_status', 'enrol_stripepaymentpro'));
        $PAGE->set_url($CFG->wwwroot . '/enrol/stripepaymentpro/subscription.php');

        $is_admin = is_siteadmin($USER->id);
        $subscription_controller = new subscription_controller();

        // Handle subscription cancellation
        $subid = optional_param('subid', null, PARAM_RAW);
        if ($subid !== null) {
            $subscription_data = $subscription_controller->get_subscriptions($userid, $is_admin);
            $allowed_subscriptions = array_column($subscription_data['stripe_subscriptions'], 'id');
            
            if ($subscription_controller->cancel_subscription($subid, $allowed_subscriptions)) {
                redirect($CFG->wwwroot . '/enrol/stripepaymentpro/subscription.php', 
                    get_string('cancelsuccess', 'enrol_stripepaymentpro'));
            } else {
                redirect($CFG->wwwroot . '/enrol/stripepaymentpro/subscription.php', 
                    get_string('cancelfailed', 'enrol_stripepaymentpro'));
            }
        }

        // Get subscription data
        $subscription_data = $subscription_controller->get_subscriptions($userid, $is_admin);
        $template_data = $subscription_controller->prepare_template_data(
            $subscription_data['stripe_subscriptions'],
            $subscription_data['subscriptions_by_id'],
            $is_admin
        );

        // Page header
        echo $OUTPUT->header();

        // Render template
        echo $OUTPUT->render_from_template('enrol_stripepaymentpro/mysubscription', $template_data);

        // Page footer
        echo $OUTPUT->footer();
    }

    /**
     * Handle coupon management page
     * 
     * @return void
     */
    public static function handle_coupon_page() {
        global $CFG, $PAGE, $OUTPUT, $USER;

        require_login();

        // Check if the user is an admin
        if (!is_siteadmin($USER->id)) {
            redirect($CFG->wwwroot);
        }

        // Set up page
        $PAGE->set_context(\context_system::instance());
        $PAGE->set_title(get_string('coupon_management', 'enrol_stripepaymentpro'));
        $PAGE->set_heading(get_string('coupon_management', 'enrol_stripepaymentpro'));
        $PAGE->set_url($CFG->wwwroot . '/enrol/stripepaymentpro/coupon_management.php');
        $PAGE->set_pagelayout('admin');

        $coupon_controller = new coupon_controller();
        $return = new \moodle_url('/enrol/stripepaymentpro/coupon_management.php');

        // Get coupon data
        $coupon_data = $coupon_controller->get_coupons_data();
        $stripe_course_list = $coupon_controller->get_stripe_courses();

        // Handle form submission
        require_once($CFG->dirroot . '/enrol/stripepaymentpro/classes/form/coupon_form.php');
        $form_action_url = new \moodle_url('/enrol/stripepaymentpro/coupon_management.php');
        $mform = new \enrol_stripepaymentpro\form\coupon_form($form_action_url, [
            $stripe_course_list,
            enrol_get_plugin('stripepaymentpro'),
            enrol_get_plugin('stripepayment')
        ]);

        if ($mform->is_cancelled()) {
            redirect($return);
        } else if ($formdata = $mform->get_data()) {
            if ($coupon_controller->create_coupon($formdata)) {
                \core\notification::success(get_string('coupon_created_success', 'enrol_stripepaymentpro'));
            }
            redirect($return);
        }

        // Capture form output
        ob_start();
        $mform->display();
        $form_html = ob_get_clean();

        // Prepare template data including the form HTML
        $template_data = [
            'coupons' => $coupon_controller->prepare_coupon_template_data(
                $coupon_data['coupons_by_course'],
                $coupon_data['coupons_list'],
                $coupon_data['courses']
            ),
            'has_coupons' => $coupon_data['has_coupons'],
            'form_html' => $form_html
        ];

        echo $OUTPUT->header();

        // Render coupon management template with form included
        echo $OUTPUT->render_from_template('enrol_stripepaymentpro/coupon_management', $template_data);

        // Initialize coupon management JavaScript module (no need to pass form HTML)
        $PAGE->requires->js_call_amd('enrol_stripepaymentpro/coupon_management', 'init', []);

        echo $OUTPUT->footer();
    }

    /**
     * Handle payment success page
     * 
     * @return void
     */
    public static function handle_payment_success() {
        global $CFG;

        require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');
        
        \Stripe\Stripe::setApiKey(get_config('enrol_stripepayment', 'secretkey'));

        // Get the session ID from the URL parameter
        $session_id = optional_param('session_id', '', PARAM_RAW);

        $webhook_controller = new enrol_stripepaymentpro_webhook_controller();
        $webhook_controller->handle_payment_success($session_id);
    }
}
