define(["core/ajax"],function(e){"use strict";function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=t(e);const{call:o}=n.default,r=(e,t,n)=>o([{methodname:"moodle_stripepaymentpro_stripe_enrol",args:{userid:e,couponid:t,instanceid:n}}])[0],s=e=>{const t=new Map;return{getelement(n){const o=`${n}-${e}`;return t.has(o)||t.set(o,document.getElementById(o)),t.get(o)},setelement(e,t){const n=this.getelement(e);n&&(n.innerHTML=t)},toggleelement(e,t){const n=this.getelement(e);n&&(n.style.display=t?"block":"none")},focuselement(e){const t=this.getelement(e);t&&t.focus()},setbutton(e,t,n,o=(t?"0.7":"1")){const r=this.getelement(e);r&&(r.disabled=t,r.textContent=n,r.style.opacity=o,r.style.cursor=t?"not-allowed":"pointer")}}};return{stripe_payment_pro:function(e,t,n,l,a,i,c,u,m){const p=s(n);let d;if(void 0===window.Stripe)return y("paymentresponse","Stripe.js library not loaded. Please check your template includes.","error"),void console.error("Stripe.js not loaded.");const y=(e,t,n)=>{let o;switch(n){case"error":o="red";break;case"success":o="green";break;default:o="blue"}p.setelement(e,`<p style="color: ${o}; font-weight: bold;">${t}</p>`),p.toggleelement(e,!0)},g=e=>{p.setelement(e,""),p.toggleelement(e,!1)},f=async e=>{e.preventDefault();const r=p.getelement("coupon"),s=r?.value.trim();if(!s)return y("showmessage",i,"error"),void p.focuselement("coupon");p.setbutton("apply",!0,c);try{const e=await((e,t)=>o([{methodname:"moodle_stripepaymentpro_applycoupon",args:{couponinput:e,instanceid:t}}])[0])(s,n);if(void 0===e?.status)throw new Error("Invalid server response");t=s,p.toggleelement("coupon",!1),p.toggleelement("apply",!1),(e=>{if(e.message?y("showmessage",e.message,"error"===e.uistate?"error":"success"):g("showmessage"),p.toggleelement("enrolbutton","paid"===e.uistate),p.toggleelement("total","paid"===e.uistate),"error"!==e.uistate){if(p.toggleelement("discountsection",e.showsections.discountsection),e.showsections.discountsection){if(e.couponname&&p.setelement("discounttag",e.couponname),e.discountamount){const t="USD"===e.currency?"$":e.currency+" ";p.setelement("discountamountdisplay",`-${t}${e.discountamount}`)}if(e.discountamount&&e.discountvalue){const t="USD"===e.currency?"$":e.currency+" ";let n="percentoff"===e.coupontype?`${e.discountvalue}% off`:`${t}${e.discountvalue} off`;e.couponduration&&("repeating"===e.couponduration&&e.coupondurationmonths?n+=` Expires in ${e.coupondurationmonths} months`:"once"!==e.couponduration&&(n+=` ${e.couponduration}`)),p.setelement("discountnote",n)}}if(e.status&&e.currency){const t=`${"USD"===e.currency?"$":e.currency+" "}${parseFloat(e.status).toFixed(2)}`,n=p.getelement("mainprice");n&&(n.textContent=t);const o=p.getelement("totalamount");o&&(o.textContent=t)}}})(e)}catch(e){y("showmessage",e.message||"Coupon validation failed","error"),p.focuselement("coupon")}finally{p.setbutton("apply",!1,"Apply")}},h=async()=>{if(p.getelement("enrolbutton")){g("paymentresponse"),p.setbutton("enrolbutton",!0,a);try{if("elements"===m&&d){let e;e="setup_intent"===d.intentType?await d.stripe.confirmSetup({elements:d.elements,confirmParams:{return_url:window.location.href}}):await d.stripe.confirmPayment({elements:d.elements,confirmParams:{return_url:window.location.href}}),e.error?(y("paymentresponse",e.error.message,"error"),p.setbutton("enrolbutton",!1,"Buy Now")):y("paymentresponse","Payment processing...","success")}else{const o=await r(e,t,n);o.error?.message?y("paymentresponse",o.error.message,"error"):"success"===o.status&&o.redirecturl?window.location.href=o.redirecturl:y("paymentresponse","Unknown error occurred during payment.","error")}}catch(e){y("paymentresponse",e.message,"error")}finally{"elements"!==m&&p.toggleelement("enrolbutton",!1)}}};if("elements"===m){p.toggleelement("enrolbutton",!1),p.toggleelement("payment-element",!0);const o=Stripe(l);(async()=>{const s=p.getelement("payment-element");if(!s)return y("paymentresponse","Payment element container (ID: payment-element) not found in HTML. Check your template.","error"),p.toggleelement("enrolbutton",!0),void p.setbutton("enrolbutton",!1,"Buy Now");g("paymentresponse");try{const l=await r(e,t,n);if(console.log("Stripe Elements response:",l),!l||!l.paymentintent)throw new Error("Invalid response from server: missing payment intent data");let a;try{a="string"==typeof l.paymentintent?JSON.parse(l.paymentintent):l.paymentintent}catch(e){throw new Error("Invalid JSON in payment intent: "+e.message)}console.log("Parsed payment intent data:",a);let i=a.client_secret;if(!i)throw new Error("Failed to get a valid client secret for payment initialization. Check server logs.");i=decodeURIComponent(i);const c=o.elements({clientSecret:i});c.create("payment").mount(`#${s.id}`),d={stripe:o,elements:c,intentType:a.type||"payment_intent"},p.toggleelement("enrolbutton",!0),p.setbutton("enrolbutton",!1,"Buy Now")}catch(e){console.error("Stripe Elements initialization error:",e),y("paymentresponse",e.message||"Stripe initialization error. Check console.","error"),p.toggleelement("enrolbutton",!0),p.setbutton("enrolbutton",!1,"Buy Now")}})()}else p.toggleelement("payment-element",!1),p.toggleelement("enrolbutton",!0),p.setbutton("enrolbutton",!1,"Buy Now");[{id:"apply",event:"click",handler:f},{id:"enrolbutton",event:"click",handler:h}].forEach(({id:e,event:t,handler:n})=>{const o=p.getelement(e);o&&o.addEventListener(t,n)})}}});
